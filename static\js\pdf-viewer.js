/**
 * PDF Viewer JavaScript - Embedded PDF viewer using PDF.js
 * Provides secure, in-page PDF viewing with navigation and zoom controls
 */

class PDFViewer {
    constructor() {
        this.pdfDoc = null;
        this.pageNum = 1;
        this.pageCount = 0;
        this.scale = 1.0;
        this.canvas = null;
        this.ctx = null;
        this.isInitialized = false;
        this.isFullscreen = false;
        
        // Initialize PDF.js worker
        if (typeof pdfjsLib !== 'undefined') {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 
                'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.worker.min.js';
        }
    }

    async initialize() {
        if (this.isInitialized || !window.pdfUrl) {
            return;
        }

        try {
            // Get DOM elements
            this.canvas = document.getElementById('pdf-canvas');
            this.ctx = this.canvas.getContext('2d');
            
            if (!this.canvas || !this.ctx) {
                throw new Error('Canvas element not found');
            }

            // Show loading indicator
            this.showLoading();
            
            // Load PDF document
            const loadingTask = pdfjsLib.getDocument(window.pdfUrl);
            this.pdfDoc = await loadingTask.promise;
            this.pageCount = this.pdfDoc.numPages;
            
            // Update page count display
            document.getElementById('pdf-page-count').textContent = this.pageCount;
            
            // Render first page
            await this.renderPage(1);
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Hide loading indicator
            this.hideLoading();
            
            this.isInitialized = true;
            
            console.log('PDF Viewer initialized successfully');
            
        } catch (error) {
            console.error('Error initializing PDF viewer:', error);
            this.showError(error.message);
        }
    }

    async renderPage(pageNumber) {
        if (!this.pdfDoc || pageNumber < 1 || pageNumber > this.pageCount) {
            return;
        }

        try {
            // Get page
            const page = await this.pdfDoc.getPage(pageNumber);
            
            // Calculate scale to fit container
            const container = document.getElementById('pdf-canvas-container');
            const containerWidth = container.clientWidth - 64; // Account for padding
            const viewport = page.getViewport({ scale: 1.0 });
            
            // Auto-scale to fit width, but respect user zoom
            const autoScale = containerWidth / viewport.width;
            const finalScale = this.scale * autoScale;
            
            const scaledViewport = page.getViewport({ scale: finalScale });
            
            // Set canvas dimensions
            this.canvas.height = scaledViewport.height;
            this.canvas.width = scaledViewport.width;
            
            // Render page
            const renderContext = {
                canvasContext: this.ctx,
                viewport: scaledViewport
            };
            
            await page.render(renderContext).promise;
            
            // Update page number display
            this.pageNum = pageNumber;
            document.getElementById('pdf-page-num').textContent = pageNumber;
            
            // Update navigation buttons
            this.updateNavigationButtons();
            
        } catch (error) {
            console.error('Error rendering page:', error);
            this.showError('Failed to render PDF page');
        }
    }

    setupEventListeners() {
        // Navigation buttons
        document.getElementById('pdf-prev-page').addEventListener('click', () => {
            if (this.pageNum > 1) {
                this.renderPage(this.pageNum - 1);
            }
        });

        document.getElementById('pdf-next-page').addEventListener('click', () => {
            if (this.pageNum < this.pageCount) {
                this.renderPage(this.pageNum + 1);
            }
        });

        // Zoom buttons
        document.getElementById('pdf-zoom-in').addEventListener('click', () => {
            this.scale = Math.min(this.scale * 1.2, 3.0);
            this.updateZoomDisplay();
            this.renderPage(this.pageNum);
        });

        document.getElementById('pdf-zoom-out').addEventListener('click', () => {
            this.scale = Math.max(this.scale / 1.2, 0.5);
            this.updateZoomDisplay();
            this.renderPage(this.pageNum);
        });

        // Fullscreen button
        document.getElementById('pdf-fullscreen').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (!this.isViewerVisible()) return;
            
            switch(e.key) {
                case 'ArrowLeft':
                case 'PageUp':
                    e.preventDefault();
                    if (this.pageNum > 1) {
                        this.renderPage(this.pageNum - 1);
                    }
                    break;
                case 'ArrowRight':
                case 'PageDown':
                    e.preventDefault();
                    if (this.pageNum < this.pageCount) {
                        this.renderPage(this.pageNum + 1);
                    }
                    break;
                case 'Home':
                    e.preventDefault();
                    this.renderPage(1);
                    break;
                case 'End':
                    e.preventDefault();
                    this.renderPage(this.pageCount);
                    break;
                case 'Escape':
                    if (this.isFullscreen) {
                        this.toggleFullscreen();
                    }
                    break;
            }
        });

        // Window resize handler
        window.addEventListener('resize', () => {
            if (this.isInitialized && this.isViewerVisible()) {
                setTimeout(() => {
                    this.renderPage(this.pageNum);
                }, 100);
            }
        });
    }

    updateNavigationButtons() {
        const prevBtn = document.getElementById('pdf-prev-page');
        const nextBtn = document.getElementById('pdf-next-page');
        
        prevBtn.disabled = this.pageNum <= 1;
        nextBtn.disabled = this.pageNum >= this.pageCount;
        
        prevBtn.style.opacity = this.pageNum <= 1 ? '0.5' : '1';
        nextBtn.style.opacity = this.pageNum >= this.pageCount ? '0.5' : '1';
    }

    updateZoomDisplay() {
        const zoomDisplay = document.getElementById('pdf-zoom-level');
        zoomDisplay.textContent = Math.round(this.scale * 100) + '%';
    }

    toggleFullscreen() {
        const container = document.getElementById('pdf-viewer-container');
        
        if (!this.isFullscreen) {
            container.classList.add('pdf-viewer-fullscreen');
            this.isFullscreen = true;
            
            // Update fullscreen button icon
            const fullscreenBtn = document.getElementById('pdf-fullscreen');
            fullscreenBtn.innerHTML = `
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            `;
            fullscreenBtn.title = 'Exit Fullscreen';
            
        } else {
            container.classList.remove('pdf-viewer-fullscreen');
            this.isFullscreen = false;
            
            // Restore fullscreen button icon
            const fullscreenBtn = document.getElementById('pdf-fullscreen');
            fullscreenBtn.innerHTML = `
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                </svg>
            `;
            fullscreenBtn.title = 'Fullscreen';
        }
        
        // Re-render current page to adjust to new container size
        setTimeout(() => {
            this.renderPage(this.pageNum);
        }, 100);
    }

    showLoading() {
        document.getElementById('pdf-loading').style.display = 'flex';
        document.getElementById('pdf-error').style.display = 'none';
        document.getElementById('pdf-canvas-container').style.display = 'none';
    }

    hideLoading() {
        document.getElementById('pdf-loading').style.display = 'none';
        document.getElementById('pdf-canvas-container').style.display = 'flex';
    }

    showError(message) {
        document.getElementById('pdf-loading').style.display = 'none';
        document.getElementById('pdf-canvas-container').style.display = 'none';
        document.getElementById('pdf-error').style.display = 'flex';
        
        const errorMessage = document.querySelector('.pdf-error-message');
        if (errorMessage) {
            errorMessage.textContent = message || 'An error occurred while loading the PDF.';
        }
    }

    isViewerVisible() {
        const container = document.getElementById('pdf-viewer-container');
        return container && container.style.display !== 'none';
    }

    reset() {
        this.pageNum = 1;
        this.scale = 1.0;
        this.isInitialized = false;
        this.isFullscreen = false;
        this.updateZoomDisplay();
    }
}

// Global PDF viewer instance
let pdfViewer = null;

// Initialize PDF viewer function (called from template)
function initializePdfViewer() {
    if (!pdfViewer) {
        pdfViewer = new PDFViewer();
    }
    
    if (!pdfViewer.isInitialized) {
        pdfViewer.initialize();
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Create global PDF viewer instance
    pdfViewer = new PDFViewer();
    
    // Auto-initialize if viewer is visible
    const container = document.getElementById('pdf-viewer-container');
    if (container && container.style.display !== 'none') {
        initializePdfViewer();
    }
});

// Security measures
document.addEventListener('contextmenu', function(e) {
    const pdfContainer = document.getElementById('pdf-viewer-container');
    if (pdfContainer && pdfContainer.contains(e.target)) {
        e.preventDefault();
        return false;
    }
});

document.addEventListener('selectstart', function(e) {
    const pdfContainer = document.getElementById('pdf-viewer-container');
    if (pdfContainer && pdfContainer.contains(e.target)) {
        e.preventDefault();
        return false;
    }
});

// Disable certain keyboard shortcuts within PDF viewer
document.addEventListener('keydown', function(e) {
    const pdfContainer = document.getElementById('pdf-viewer-container');
    if (pdfContainer && pdfContainer.contains(e.target)) {
        if (e.key === 'F12' || 
            (e.ctrlKey && (e.key === 'u' || e.key === 'U' || e.key === 's' || e.key === 'S'))) {
            e.preventDefault();
            return false;
        }
    }
});
