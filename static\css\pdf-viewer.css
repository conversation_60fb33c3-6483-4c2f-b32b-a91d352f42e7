/* PDF Viewer Styles - Using preferred color palette */
/* Colors: #F1EFEC (offwhite), #D4C9BE (light), #123458 (dark blue), #030303 (black), gray800 */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

.pdf-viewer-container {
    background: #F1EFEC;
    border: 2px solid #D4C9BE;
    border-radius: 12px;
    margin: 2rem 0;
    box-shadow: 0 10px 25px rgba(18, 52, 88, 0.1);
    overflow: hidden;
    font-family: 'Poppins', sans-serif;
}

.pdf-viewer-wrapper {
    position: relative;
    background: white;
}

/* PDF Viewer Header */
.pdf-viewer-header {
    background: linear-gradient(135deg, #123458 0%, #1e4a73 100%);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 3px solid #D4C9BE;
}

.pdf-viewer-title {
    display: flex;
    align-items: center;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
}

.pdf-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pdf-control-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pdf-control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.pdf-control-btn:active {
    transform: translateY(0);
}

.pdf-page-info {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
}

.pdf-zoom-display {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    min-width: 50px;
    text-align: center;
}

.pdf-divider {
    width: 1px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    margin: 0 0.5rem;
}

.pdf-close-btn {
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: white;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
}

.pdf-close-btn:hover {
    background: rgba(239, 68, 68, 0.3);
    border-color: rgba(239, 68, 68, 0.4);
}

/* PDF Canvas Container */
.pdf-canvas-container {
    position: relative;
    background: #F1EFEC;
    min-height: 600px;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 2rem;
    overflow: auto;
}

.pdf-canvas {
    max-width: 100%;
    height: auto;
    box-shadow: 0 8px 32px rgba(18, 52, 88, 0.15);
    border-radius: 8px;
    background: white;
}

/* Loading Indicator */
.pdf-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    background: #F1EFEC;
}

.pdf-loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid #D4C9BE;
    border-top: 4px solid #123458;
    border-radius: 50%;
    animation: pdf-spin 1s linear infinite;
    margin-bottom: 1rem;
}

.pdf-loading-text {
    color: #123458;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    font-size: 1.1rem;
}

@keyframes pdf-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error State */
.pdf-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    background: #F1EFEC;
    text-align: center;
}

.pdf-error-icon {
    margin-bottom: 1rem;
}

.pdf-error-title {
    color: #123458;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.pdf-error-message {
    color: #6b7280;
    font-family: 'Poppins', sans-serif;
    margin-bottom: 2rem;
    max-width: 400px;
}

.pdf-error-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.pdf-retry-btn, .pdf-download-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
}

.pdf-retry-btn {
    background: #123458;
    color: white;
}

.pdf-retry-btn:hover {
    background: #1e4a73;
    transform: translateY(-1px);
}

.pdf-download-btn {
    background: #D4C9BE;
    color: #123458;
}

.pdf-download-btn:hover {
    background: #c4b9ae;
    transform: translateY(-1px);
}

/* Security Watermarks */
.pdf-watermarks {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
}

.pdf-watermark {
    position: absolute;
    background: rgba(18, 52, 88, 0.05);
    color: rgba(18, 52, 88, 0.3);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    backdrop-filter: blur(2px);
}

.pdf-watermark-top-left {
    top: 1rem;
    left: 1rem;
}

.pdf-watermark-top-right {
    top: 1rem;
    right: 1rem;
}

.pdf-watermark-bottom-left {
    bottom: 1rem;
    left: 1rem;
}

.pdf-watermark-bottom-right {
    bottom: 1rem;
    right: 1rem;
}

.pdf-watermark-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    background: rgba(18, 52, 88, 0.03);
    color: rgba(18, 52, 88, 0.15);
    padding: 2rem 4rem;
    border-radius: 12px;
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 2rem;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    backdrop-filter: blur(1px);
    border: 2px dashed rgba(18, 52, 88, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .pdf-viewer-header {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .pdf-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .pdf-canvas-container {
        padding: 1rem;
    }
    
    .pdf-watermark-center {
        font-size: 1.5rem;
        padding: 1.5rem 3rem;
    }
    
    .pdf-error-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* Fullscreen Mode */
.pdf-viewer-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background: white;
}

.pdf-viewer-fullscreen .pdf-canvas-container {
    height: calc(100vh - 80px);
}

/* Print Styles */
@media print {
    .pdf-viewer-header,
    .pdf-watermarks {
        display: none;
    }
    
    .pdf-canvas-container {
        padding: 0;
        background: white;
    }
}
