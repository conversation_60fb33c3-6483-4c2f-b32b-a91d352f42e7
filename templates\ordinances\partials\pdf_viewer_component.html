{% load static %}

<!-- PDF Viewer Component -->
<div id="pdf-viewer-container" class="pdf-viewer-container" style="display: none;">
    <div class="pdf-viewer-wrapper">
        <!-- PDF Viewer Header -->
        <div class="pdf-viewer-header">
            <div class="pdf-viewer-title">
                <svg class="h-6 w-6 text-red-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span class="font-semibold">Official PDF Document</span>
                <span class="text-sm text-gray-600 ml-2">{{ ordinance.ordinance_number }}</span>
            </div>
            
            <!-- PDF Controls -->
            <div class="pdf-controls">
                <button id="pdf-prev-page" class="pdf-control-btn" title="Previous Page">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>
                
                <div class="pdf-page-info">
                    <span id="pdf-page-num">1</span>
                    <span class="text-gray-500">/</span>
                    <span id="pdf-page-count">1</span>
                </div>
                
                <button id="pdf-next-page" class="pdf-control-btn" title="Next Page">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                </button>
                
                <div class="pdf-divider"></div>
                
                <button id="pdf-zoom-out" class="pdf-control-btn" title="Zoom Out">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7" />
                    </svg>
                </button>
                
                <span id="pdf-zoom-level" class="pdf-zoom-display">100%</span>
                
                <button id="pdf-zoom-in" class="pdf-control-btn" title="Zoom In">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                    </svg>
                </button>
                
                <div class="pdf-divider"></div>
                
                <button id="pdf-fullscreen" class="pdf-control-btn" title="Fullscreen">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                    </svg>
                </button>
                
                <button onclick="togglePdfViewer()" class="pdf-close-btn" title="Close Viewer">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- PDF Loading Indicator -->
        <div id="pdf-loading" class="pdf-loading">
            <div class="pdf-loading-spinner"></div>
            <p class="pdf-loading-text">Loading PDF document...</p>
        </div>
        
        <!-- PDF Error Message -->
        <div id="pdf-error" class="pdf-error" style="display: none;">
            <div class="pdf-error-icon">
                <svg class="h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>
            <h3 class="pdf-error-title">Unable to Load PDF</h3>
            <p class="pdf-error-message">The PDF document could not be loaded. Please try again or contact support.</p>
            <div class="pdf-error-actions">
                <button onclick="initializePdfViewer()" class="pdf-retry-btn">
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Retry
                </button>
                <a href="{{ pdf_url }}" target="_blank" class="pdf-download-btn">
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Download PDF
                </a>
            </div>
        </div>
        
        <!-- PDF Canvas Container -->
        <div id="pdf-canvas-container" class="pdf-canvas-container">
            <canvas id="pdf-canvas" class="pdf-canvas"></canvas>
        </div>
        
        <!-- Security Watermarks -->
        <div class="pdf-watermarks">
            <div class="pdf-watermark pdf-watermark-top-left">OFFICIAL DOCUMENT</div>
            <div class="pdf-watermark pdf-watermark-top-right">{{ ordinance.ordinance_number }}</div>
            <div class="pdf-watermark pdf-watermark-bottom-left">CONFIDENTIAL</div>
            <div class="pdf-watermark pdf-watermark-bottom-right">{{ ordinance.year_passed }}</div>
            <div class="pdf-watermark-center">OFFICIAL DOCUMENT</div>
        </div>
    </div>
</div>

<!-- PDF.js CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.4.120/pdf.min.js"></script>

<script>
    // Initialize PDF viewer with the provided URL
    window.pdfUrl = "{{ pdf_url }}";
    window.ordinanceTitle = "{{ ordinance.title|escapejs }}";
    window.ordinanceNumber = "{{ ordinance.ordinance_number|escapejs }}";
</script>
